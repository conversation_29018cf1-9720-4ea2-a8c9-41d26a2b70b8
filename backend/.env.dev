# ==================== 开发环境配置 ====================
# AI Travel Planner - 开发环境环境变量

# ==================== 应用配置 ====================
APP_NAME=ai-travel-planner
APP_VERSION=1.0.0
ENV=development
DEBUG=true

# ==================== 数据库配置 ====================
# MySQL配置
MYSQL_ROOT_PASSWORD=ai_travel_root
MYSQL_DATABASE=ai_travel_db
MYSQL_USER=root
MYSQL_PASSWORD=ai_travel_root

# 数据库连接配置 (Docker服务名)
DATABASE_URL=mysql+aiomysql://ai_travel_user:ai_travel_pass@mysql:3306/ai_travel_db
DATABASE_POOL_SIZE=10
DATABASE_MAX_OVERFLOW=20
ENABLE_QUERY_LOG=true

# ==================== 缓存配置 ====================
# Redis配置 (Docker服务名)
REDIS_URL=redis://redis:6379/0
REDIS_PASSWORD=ai_travel_redis
REDIS_SESSION_DB=1
REDIS_CACHE_DB=2
REDIS_QUEUE_DB=3
REDIS_AGENT_DB=4

# ==================== AI服务配置 ====================
# OpenAI配置 (或使用国内代理)
OPENAI_API_KEY=sk-R2utGqjya03Bgnc77273729e7b73457e97A140D79bF3B944
OPENAI_API_BASE=https://api.apiyi.com/v1
OPENAI_MODEL=gpt-4-turbo-preview
OPENAI_EMBEDDING_MODEL=text-embedding-3-small

# 国产大模型配置 (可选)
BAIDU_QIANFAN_API_KEY=your_baidu_qianfan_api_key_here
BAIDU_QIANFAN_SECRET_KEY=your_baidu_qianfan_secret_key_here
ALIBABA_DASHSCOPE_API_KEY=your_alibaba_dashscope_api_key_here
TENCENT_HUNYUAN_SECRET_ID=your_tencent_hunyuan_secret_id_here
TENCENT_HUNYUAN_SECRET_KEY=your_tencent_hunyuan_secret_key_here

# vLLM配置
VLLM_URL=http://vllm-service:8001
VLLM_MODEL=qwen2.5-7b-instruct

# ==================== RAG配置 ====================
# Qdrant向量数据库 (Docker服务名)
QDRANT_URL=http://qdrant:6333
QDRANT_API_KEY=
QDRANT_COLLECTION_NAME=travel_knowledge

# Elasticsearch搜索引擎
ELASTICSEARCH_URL=http://elasticsearch:9200
ELASTICSEARCH_INDEX=travel_documents

# ==================== n8n工作流配置 ====================
N8N_HOST=n8n
N8N_BASIC_AUTH_USER=admin
N8N_BASIC_AUTH_PASSWORD=ai_travel_n8n

# ==================== 安全配置 ====================
# JWT密钥
JWT_SECRET=ai_travel_jwt_secret_key_minimum_32_characters_dev
JWT_ALGORITHM=HS256
JWT_EXPIRE_MINUTES=1440

# OAuth2配置
OAUTH2_SECRET_KEY=ai_travel_oauth2_secret_dev

# 加密密钥
ENCRYPTION_KEY=ai_travel_encryption_key_dev_32_chars

# ==================== 外部API配置 - 开发环境 ====================
# 地图服务API (中国大陆)
BAIDU_MAP_API_KEY=your_baidu_map_api_key_here
BAIDU_MAP_API_URL=https://api.map.baidu.com

AMAP_API_KEY=your_amap_api_key_here
AMAP_API_URL=https://restapi.amap.com

TENCENT_MAP_API_KEY=your_tencent_map_api_key_here
TENCENT_MAP_API_URL=https://apis.map.qq.com

# 天气服务API (中国大陆)
CAIYUN_WEATHER_API_KEY=your_caiyun_weather_api_key_here
CAIYUN_WEATHER_API_URL=https://api.caiyunapp.com

HEWEATHER_API_KEY=your_heweather_api_key_here
HEWEATHER_API_URL=https://api.qweather.com

XINZHI_WEATHER_API_KEY=your_xinzhi_weather_api_key_here
XINZHI_WEATHER_API_URL=https://api.seniverse.com

# ==================== MCP配置 ====================
MCP_SERVER_HOST=integration-service
MCP_SERVER_PORT=8005
MCP_WEBSOCKET_URL=ws://integration-service:8005/mcp

# ==================== 工作流配置 ====================
WORKFLOW_ENGINE_URL=http://n8n:5678
WORKFLOW_WEBHOOK_BASE_URL=http://localhost:5678/webhook

# ==================== 监控配置 ====================
# Prometheus
PROMETHEUS_URL=http://prometheus:9090

# Grafana
GRAFANA_ADMIN_PASSWORD=ai_travel_grafana
GRAFANA_DATABASE_URL=mysql://ai_travel_user:ai_travel_pass@mysql:3306/grafana

# ==================== 邮件配置 - 开发环境 ====================
# 163邮箱
SMTP_HOST=smtp.163.com
SMTP_PORT=465
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_email_auth_code_here
SMTP_FROM_NAME=AI Travel Planner Dev
SMTP_USE_SSL=true

# ==================== 文件存储配置 - 开发环境 ====================
# 本地存储
UPLOAD_DIR=/app/uploads
MAX_FILE_SIZE=10485760

# ==================== 日志配置 ====================
LOG_LEVEL=DEBUG
LOG_FORMAT=text
LOG_FILE=/app/logs/app.log
LOG_MAX_SIZE=100MB
LOG_BACKUP_COUNT=5

# ==================== 性能配置 ====================
# 限流配置
RATE_LIMIT_PER_MINUTE=120
RATE_LIMIT_BURST=200

# 缓存配置
CACHE_TTL=1800
CACHE_MAX_SIZE=500

# ==================== 部署配置 ====================
# 域名配置
DOMAIN_NAME=localhost
ALLOWED_HOSTS=localhost,127.0.0.1,0.0.0.0
CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

# ==================== 特性开关 ====================
FEATURE_ENABLE_REGISTRATION=true
FEATURE_ENABLE_SOCIAL_LOGIN=false
FEATURE_ENABLE_PAYMENT=false
FEATURE_ENABLE_ANALYTICS=false
FEATURE_ENABLE_CHAT_HISTORY=true
FEATURE_ENABLE_FILE_UPLOAD=true

# ==================== 开发环境特定配置 ====================
# 时区设置
TIMEZONE=Asia/Shanghai

# 地区设置
DEFAULT_COUNTRY=CN
DEFAULT_LANGUAGE=zh-CN
DEFAULT_CURRENCY=CNY

# 内容审核 (开发环境关闭)
CONTENT_MODERATION_ENABLED=false
