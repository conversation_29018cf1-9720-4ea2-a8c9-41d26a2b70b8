# ==================== 生产环境配置 ====================
# AI Travel Planner - 生产环境环境变量模板
# 复制此文件为 .env.prod 并填入实际值

# ==================== 应用配置 ====================
APP_NAME=ai-travel-planner
APP_VERSION=1.0.0
ENV=production
DEBUG=false

# ==================== 数据库配置 ====================
# MySQL配置
MYSQL_ROOT_PASSWORD=your_secure_root_password_here
MYSQL_DATABASE=ai_travel_planner
MYSQL_USER=ai_travel_user
MYSQL_PASSWORD=your_secure_mysql_password_here

# 数据库连接配置
DATABASE_URL=mysql+aiomysql://ai_travel_user:your_secure_mysql_password_here@mysql-prod:3306/ai_travel_planner
DATABASE_POOL_SIZE=20
DATABASE_MAX_OVERFLOW=40
ENABLE_QUERY_LOG=false

# ==================== 缓存配置 ====================
# Redis配置
REDIS_URL=redis://redis-prod:6379/0
REDIS_SESSION_DB=1
REDIS_CACHE_DB=2
REDIS_QUEUE_DB=3
REDIS_AGENT_DB=4

# ==================== AI服务配置 ====================
# OpenAI配置 (或使用国内代理)
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_API_BASE=https://api.openai.com/v1
OPENAI_MODEL=gpt-4-turbo-preview
OPENAI_EMBEDDING_MODEL=text-embedding-3-small

# 国产大模型配置 (可选)
BAIDU_QIANFAN_API_KEY=your_baidu_qianfan_api_key_here
BAIDU_QIANFAN_SECRET_KEY=your_baidu_qianfan_secret_key_here
ALIBABA_DASHSCOPE_API_KEY=your_alibaba_dashscope_api_key_here
TENCENT_HUNYUAN_SECRET_ID=your_tencent_hunyuan_secret_id_here
TENCENT_HUNYUAN_SECRET_KEY=your_tencent_hunyuan_secret_key_here

# vLLM配置
VLLM_URL=http://vllm-service-prod:8001
VLLM_MODEL=qwen2.5-7b-instruct

# ==================== RAG配置 ====================
# Qdrant向量数据库
QDRANT_URL=http://qdrant-prod:6333
QDRANT_API_KEY=your_qdrant_api_key_here
QDRANT_COLLECTION_NAME=travel_knowledge

# Elasticsearch搜索引擎
ELASTICSEARCH_URL=http://elasticsearch-prod:9200
ELASTICSEARCH_INDEX=travel_documents

# ==================== n8n工作流配置 ====================
N8N_HOST=localhost
N8N_BASIC_AUTH_USER=admin
N8N_BASIC_AUTH_PASSWORD=your_secure_n8n_password_here

# ==================== 安全配置 ====================
# JWT密钥
JWT_SECRET=your_jwt_secret_key_here_minimum_32_characters
JWT_ALGORITHM=HS256
JWT_EXPIRE_MINUTES=1440

# OAuth2配置
OAUTH2_SECRET_KEY=your_oauth2_secret_key_here

# 加密密钥
ENCRYPTION_KEY=your_encryption_key_here_32_characters

# ==================== 外部API配置 - 中国大陆服务 ====================
# 在线旅游平台API
CTRIP_API_KEY=your_ctrip_api_key_here
CTRIP_API_SECRET=your_ctrip_api_secret_here
CTRIP_API_URL=https://openapi.ctrip.com

QUNAR_API_KEY=your_qunar_api_key_here
QUNAR_API_SECRET=your_qunar_api_secret_here
QUNAR_API_URL=https://open.qunar.com

FLIGGY_API_KEY=your_fliggy_api_key_here
FLIGGY_APP_SECRET=your_fliggy_app_secret_here
FLIGGY_API_URL=https://eco.taobao.com/router/rest

MEITUAN_API_KEY=your_meituan_api_key_here
MEITUAN_API_SECRET=your_meituan_api_secret_here
MEITUAN_API_URL=https://api.meituan.com

# 地图服务API (中国大陆)
BAIDU_MAP_API_KEY=your_baidu_map_api_key_here
BAIDU_MAP_API_URL=https://api.map.baidu.com

AMAP_API_KEY=your_amap_api_key_here
AMAP_API_URL=https://restapi.amap.com

TENCENT_MAP_API_KEY=your_tencent_map_api_key_here
TENCENT_MAP_API_URL=https://apis.map.qq.com

# 天气服务API (中国大陆)
CAIYUN_WEATHER_API_KEY=your_caiyun_weather_api_key_here
CAIYUN_WEATHER_API_URL=https://api.caiyunapp.com

HEWEATHER_API_KEY=your_heweather_api_key_here
HEWEATHER_API_URL=https://api.qweather.com

XINZHI_WEATHER_API_KEY=your_xinzhi_weather_api_key_here
XINZHI_WEATHER_API_URL=https://api.seniverse.com

# ==================== MCP配置 ====================
MCP_SERVER_HOST=localhost
MCP_SERVER_PORT=8005
MCP_WEBSOCKET_URL=ws://localhost:8005/mcp

# ==================== 工作流配置 ====================
WORKFLOW_ENGINE_URL=http://n8n-prod:5678
WORKFLOW_WEBHOOK_BASE_URL=http://localhost:5678/webhook

# ==================== 监控配置 ====================
# Prometheus
PROMETHEUS_URL=http://prometheus-prod:9090

# Grafana
GRAFANA_ADMIN_PASSWORD=your_secure_grafana_password_here
GRAFANA_DATABASE_URL=mysql://ai_travel_user:your_secure_mysql_password_here@mysql-prod:3306/grafana

# ==================== 邮件配置 - 中国大陆服务 ====================
# 163邮箱
SMTP_HOST=smtp.163.com
SMTP_PORT=465
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_email_auth_code_here
SMTP_FROM_NAME=AI Travel Planner
SMTP_USE_SSL=true

# QQ邮箱 (备选)
QQ_SMTP_HOST=smtp.qq.com
QQ_SMTP_PORT=587
QQ_SMTP_USER=<EMAIL>
QQ_SMTP_PASSWORD=your_qq_auth_code_here

# 企业邮箱 (腾讯企业邮箱)
TENCENT_SMTP_HOST=smtp.exmail.qq.com
TENCENT_SMTP_PORT=465
TENCENT_SMTP_USER=<EMAIL>
TENCENT_SMTP_PASSWORD=your_company_email_password_here

# 阿里云邮件推送服务
ALIYUN_EMAIL_ACCESS_KEY_ID=your_aliyun_email_access_key_here
ALIYUN_EMAIL_ACCESS_KEY_SECRET=your_aliyun_email_secret_here
ALIYUN_EMAIL_REGION=cn-hangzhou
ALIYUN_EMAIL_FROM_ADDRESS=<EMAIL>

# ==================== 文件存储配置 - 中国大陆服务 ====================
# 本地存储
UPLOAD_DIR=/app/uploads
MAX_FILE_SIZE=10485760

# 阿里云OSS
ALIYUN_OSS_ACCESS_KEY_ID=your_aliyun_oss_access_key_here
ALIYUN_OSS_ACCESS_KEY_SECRET=your_aliyun_oss_secret_here
ALIYUN_OSS_BUCKET_NAME=ai-travel-planner-files
ALIYUN_OSS_ENDPOINT=https://oss-cn-hangzhou.aliyuncs.com

# 腾讯云COS
TENCENT_COS_SECRET_ID=your_tencent_cos_secret_id_here
TENCENT_COS_SECRET_KEY=your_tencent_cos_secret_key_here
TENCENT_COS_BUCKET=ai-travel-planner-files-1234567890
TENCENT_COS_REGION=ap-beijing

# 七牛云存储
QINIU_ACCESS_KEY=your_qiniu_access_key_here
QINIU_SECRET_KEY=your_qiniu_secret_key_here
QINIU_BUCKET_NAME=ai-travel-planner
QINIU_DOMAIN=your-domain.qiniucdn.com

# ==================== 短信服务配置 - 中国大陆服务 ====================
# 阿里云短信服务
ALIYUN_SMS_ACCESS_KEY_ID=your_aliyun_sms_access_key_here
ALIYUN_SMS_ACCESS_KEY_SECRET=your_aliyun_sms_secret_here
ALIYUN_SMS_SIGN_NAME=AI旅行规划师
ALIYUN_SMS_TEMPLATE_CODE=SMS_123456789

# 腾讯云短信服务
TENCENT_SMS_SECRET_ID=your_tencent_sms_secret_id_here
TENCENT_SMS_SECRET_KEY=your_tencent_sms_secret_key_here
TENCENT_SMS_APP_ID=1234567890
TENCENT_SMS_SIGN=AI旅行规划师
TENCENT_SMS_TEMPLATE_ID=123456

# ==================== 支付配置 - 中国大陆服务 ====================
# 支付宝
ALIPAY_APP_ID=your_alipay_app_id_here
ALIPAY_PRIVATE_KEY=your_alipay_private_key_here
ALIPAY_PUBLIC_KEY=your_alipay_public_key_here
ALIPAY_GATEWAY_URL=https://openapi.alipay.com/gateway.do

# 微信支付
WECHAT_PAY_APP_ID=your_wechat_app_id_here
WECHAT_PAY_MCH_ID=your_wechat_mch_id_here
WECHAT_PAY_API_KEY=your_wechat_pay_api_key_here
WECHAT_PAY_CERT_PATH=/app/certs/apiclient_cert.pem
WECHAT_PAY_KEY_PATH=/app/certs/apiclient_key.pem

# ==================== 社交登录配置 - 中国大陆服务 ====================
# 微信开放平台
WECHAT_APP_ID=your_wechat_app_id_here
WECHAT_APP_SECRET=your_wechat_app_secret_here

# QQ登录
QQ_APP_ID=your_qq_app_id_here
QQ_APP_KEY=your_qq_app_key_here

# 微博登录
WEIBO_APP_KEY=your_weibo_app_key_here
WEIBO_APP_SECRET=your_weibo_app_secret_here

# ==================== 日志配置 ====================
LOG_LEVEL=INFO
LOG_FORMAT=json
LOG_FILE=/app/logs/app.log
LOG_MAX_SIZE=100MB
LOG_BACKUP_COUNT=10

# ==================== 性能配置 ====================
# 限流配置
RATE_LIMIT_PER_MINUTE=60
RATE_LIMIT_BURST=100

# 缓存配置
CACHE_TTL=3600
CACHE_MAX_SIZE=1000

# ==================== 部署配置 ====================
# 域名配置
DOMAIN_NAME=your-domain.com
SSL_CERT_PATH=/etc/nginx/ssl/cert.pem
SSL_KEY_PATH=/etc/nginx/ssl/key.pem

# 网络配置
ALLOWED_HOSTS=localhost,127.0.0.1,your-domain.com
CORS_ORIGINS=https://your-domain.com,http://localhost:3000

# ==================== 备份配置 ====================
BACKUP_ALIYUN_OSS_BUCKET=ai-travel-planner-backups
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30

# ==================== 特性开关 ====================
FEATURE_ENABLE_REGISTRATION=true
FEATURE_ENABLE_SOCIAL_LOGIN=true
FEATURE_ENABLE_PAYMENT=true
FEATURE_ENABLE_ANALYTICS=true
FEATURE_ENABLE_CHAT_HISTORY=true
FEATURE_ENABLE_FILE_UPLOAD=true

# ==================== 中国大陆特定配置 ====================
# 时区设置
TIMEZONE=Asia/Shanghai

# ICP备案信息
ICP_LICENSE=your_icp_license_here

# 地区设置
DEFAULT_COUNTRY=CN
DEFAULT_LANGUAGE=zh-CN
DEFAULT_CURRENCY=CNY

# 内容审核
CONTENT_MODERATION_ENABLED=true
BAIDU_TEXT_CENSOR_API_KEY=your_baidu_text_censor_api_key_here
TENCENT_CMS_SECRET_ID=your_tencent_cms_secret_id_here
TENCENT_CMS_SECRET_KEY=your_tencent_cms_secret_key_here
