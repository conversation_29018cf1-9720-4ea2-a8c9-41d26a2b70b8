# Web框架
fastapi==0.115.6
uvicorn[standard]==0.32.1
websockets==14.1
starlette==0.41.3

# AI/ML框架
langchain==0.3.12
langchain-community==0.3.12
langchain-openai==0.2.14
openai==1.61.0
transformers==4.51.1
sentence-transformers==3.3.1
vllm==0.8.5
torch==2.6.0
torchvision>=0.16.0
torchaudio>=2.1.0

# 数据验证和ORM
pydantic==2.10.4
pydantic-settings==2.7.0
sqlalchemy==2.0.36
alembic==1.14.0
asyncpg==0.30.0
aiomysql==0.2.0

# 缓存和消息队列
redis==5.2.1
celery==5.4.0
kombu==5.4.2

# 向量数据库
qdrant-client==1.14.1

# 搜索引擎
elasticsearch==8.16.0

# HTTP客户端
httpx==0.28.1
aiohttp==3.11.11
requests==2.32.3

# 数据处理
numpy>=1.24.0
pandas>=2.0.0
scikit-learn>=1.3.0
beautifulsoup4==4.12.3
lxml==5.3.0
pypdf==5.1.0
python-multipart==0.0.20

# NLP处理
nltk==3.9.1
spacy==3.8.2
jieba==0.42.1
zhipuai==2.1.5.20250801

# 图像处理
Pillow>=10.0.0

# 开发和测试工具
pytest==8.3.4
pytest-asyncio==0.25.0
pytest-cov==6.0.0
black==24.10.0
isort==5.13.2
flake8==7.1.1
mypy==1.14.0

# 日志和监控
structlog==24.4.0
loguru==0.7.3
prometheus-client==0.21.1

# 配置管理
python-dotenv==1.0.1
pyyaml==6.0.2
toml==0.10.2

# 安全
cryptography==44.0.0
passlib[bcrypt]==1.7.4
python-jose[cryptography]==3.3.0
bcrypt==4.2.1

# 异步HTTP
aiofiles==24.1.0
aiolimiter==1.1.0

# API文档
python-multipart==0.0.20

# 时间处理
arrow==1.3.0

# 网络
slowapi==0.1.9

# JSON处理  
orjson==3.10.12

# 工作流
# https://docs.n8n.io/api/using-api-playground/

# 其他工具
click==8.1.8
rich==13.9.4
tqdm==4.67.1

# 量化和压缩
bitsandbytes==0.45.0

# 模型优化
optimum==1.23.3

# 文件处理
openpyxl==3.1.5
python-docx==1.1.2

# 网络爬虫
scrapy==2.12.0

# 正则表达式
regex==2024.11.6

# 字符串相似性
fuzzywuzzy==0.18.0
python-Levenshtein==0.26.1
