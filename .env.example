# AI Travel Planner 环境变量配置示例
# 复制此文件为 .env 并修改相应的值

# ==================== 应用基础配置 ====================
APP_NAME=AI Travel Planner
APP_VERSION=1.0.0
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=INFO
HOST=0.0.0.0
PORT=8080

# ==================== 安全配置 ====================
JWT_SECRET=your_super_secret_jwt_key_here_please_change_in_production
JWT_ALGORITHM=HS256
JWT_EXPIRE_HOURS=24
JWT_REFRESH_EXPIRE_DAYS=30

ENCRYPTION_KEY=your_encryption_key_here_exactly_32
PASSWORD_SALT_ROUNDS=12

# CORS和主机配置
ALLOWED_HOSTS=*
CORS_ORIGINS=*

# ==================== 数据库配置 ====================
# MySQL数据库配置
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_DATABASE=ai_travel_db
MYSQL_USER=ai_travel_user
MYSQL_PASSWORD=ai_travel_pass
MYSQL_ROOT_PASSWORD=ai_travel_root

# 数据库连接池配置
DATABASE_POOL_SIZE=20
DATABASE_MAX_OVERFLOW=30

# ==================== 缓存配置 ====================
# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=ai_travel_redis
REDIS_DB_SESSION=0
REDIS_DB_CACHE=1
REDIS_DB_QUEUE=2
REDIS_DB_AGENT=3

# ==================== AI服务配置 ====================
# OpenAI配置
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_BASE_URL=https://api.openai.com/v1
OPENAI_MODEL=gpt-4-0125-preview
OPENAI_MAX_TOKENS=4096
OPENAI_TEMPERATURE=0.7

# vLLM配置
VLLM_URL=http://localhost:8001
VLLM_MODEL=Qwen/Qwen3-32B
VLLM_MAX_TOKENS=4096
VLLM_TEMPERATURE=0.7
VLLM_TOP_P=0.95

# ==================== 向量数据库配置 ====================
# Qdrant配置
QDRANT_URL=http://localhost:6333
QDRANT_HOST=localhost
QDRANT_PORT=6333
QDRANT_GRPC_PORT=6334
QDRANT_API_KEY=
QDRANT_COLLECTION_NAME=travel_knowledge
QDRANT_VECTOR_SIZE=384

# Embedding模型配置
EMBEDDING_MODEL=sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2
EMBEDDING_BATCH_SIZE=32

# ==================== 搜索引擎配置 ====================
ELASTICSEARCH_URL=http://localhost:9200
ELASTICSEARCH_USERNAME=
ELASTICSEARCH_PASSWORD=
ELASTICSEARCH_INDEX_PREFIX=travel

# ==================== 外部API配置 ====================
# 在线旅游平台API
CTRIP_API_KEY=
CTRIP_API_SECRET=
QUNAR_API_KEY=
QUNAR_API_SECRET=
FLIGGY_API_KEY=
FLIGGY_APP_SECRET=
MEITUAN_API_KEY=
MEITUAN_API_SECRET=

# 地图服务API
BAIDU_MAP_API_KEY=
AMAP_API_KEY=
TENCENT_MAP_API_KEY=

# 天气服务API
CAIYUN_WEATHER_API_KEY=
HEWEATHER_API_KEY=
XINZHI_WEATHER_API_KEY=

# ==================== MCP服务配置 ====================
MCP_SERVER_URL=http://localhost:8002
MCP_SERVER_HOST=0.0.0.0
MCP_SERVER_PORT=8002
MCP_TOOL_TIMEOUT=30
MCP_MAX_CONCURRENT_TOOLS=10

# ==================== 工作流配置 ====================
# n8n配置
N8N_HOST=localhost
N8N_PORT=5678
N8N_USER=admin
N8N_PASSWORD=ai_travel_n8n
N8N_WEBHOOK_URL=http://localhost:5678/webhook

# Celery配置
CELERY_BROKER_URL=redis://localhost:6379/2
CELERY_RESULT_BACKEND=redis://localhost:6379/2
CELERY_MAX_RETRIES=3
CELERY_RETRY_DELAY=60

# ==================== 监控配置 ====================
PROMETHEUS_URL=http://localhost:9090
METRICS_PORT=8080
GRAFANA_URL=http://localhost:3000
GRAFANA_USER=admin
GRAFANA_PASSWORD=ai_travel_grafana

# ==================== 限流配置 ====================
RATE_LIMIT_PER_MINUTE=60
RATE_LIMIT_PER_HOUR=1000
RATE_LIMIT_PER_DAY=10000

# ==================== 开发工具配置 ====================
TEST_DATABASE_URL=mysql+aiomysql://test_user:test_pass@localhost:3306/travel_test_db
ENABLE_PROFILER=false
ENABLE_QUERY_LOG=false
SENTRY_DSN=

# ==================== 中国大陆服务配置 ====================
# 国产大模型配置
BAIDU_QIANFAN_API_KEY=
BAIDU_QIANFAN_SECRET_KEY=
ALIBABA_DASHSCOPE_API_KEY=
TENCENT_HUNYUAN_SECRET_ID=
TENCENT_HUNYUAN_SECRET_KEY=

# 社交登录配置
WECHAT_APP_ID=
WECHAT_APP_SECRET=
QQ_APP_ID=
QQ_APP_KEY=
WEIBO_APP_KEY=
WEIBO_APP_SECRET=

# 支付服务配置
ALIPAY_APP_ID=
ALIPAY_PRIVATE_KEY=
ALIPAY_PUBLIC_KEY=
WECHAT_PAY_APP_ID=
WECHAT_PAY_MCH_ID=
WECHAT_PAY_API_KEY=

# 云存储配置
ALIYUN_OSS_ACCESS_KEY_ID=
ALIYUN_OSS_ACCESS_KEY_SECRET=
TENCENT_COS_SECRET_ID=
TENCENT_COS_SECRET_KEY=
QINIU_ACCESS_KEY=
QINIU_SECRET_KEY=

# 地区和语言设置
DEFAULT_COUNTRY=CN
DEFAULT_LANGUAGE=zh-CN
DEFAULT_CURRENCY=CNY
TIMEZONE=Asia/Shanghai

# 合规配置
ICP_LICENSE=
CONTENT_MODERATION_ENABLED=true
BAIDU_TEXT_CENSOR_API_KEY=
