.chat-window {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

/* 聊天头部 */
.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: linear-gradient(135deg, #1890ff 0%, #722ed1 100%);
  color: white;
  border-bottom: 1px solid #e8e8e8;
}

.chat-title {
  font-size: 18px;
  font-weight: 600;
  display: flex;
  align-items: center;
}

.connection-status {
  display: flex;
  align-items: center;
  font-size: 12px;
  opacity: 0.9;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 6px;
}

.status-dot.connected {
  background: #52c41a;
  box-shadow: 0 0 8px rgba(82, 196, 26, 0.6);
}

.status-dot.disconnected {
  background: #ff4d4f;
  animation: blink 1s infinite;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0.3; }
}

/* 消息容器 */
.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  background: #fafafa;
}

.messages-container::-webkit-scrollbar {
  width: 6px;
}

.messages-container::-webkit-scrollbar-track {
  background: #f0f0f0;
  border-radius: 3px;
}

.messages-container::-webkit-scrollbar-thumb {
  background: #d9d9d9;
  border-radius: 3px;
}

.messages-container::-webkit-scrollbar-thumb:hover {
  background: #bfbfbf;
}

/* 欢迎消息 */
.welcome-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 60px 20px;
  color: #666;
}

.welcome-message h3 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 20px;
  font-weight: 600;
}

.welcome-message p {
  margin: 0;
  font-size: 14px;
  line-height: 1.6;
  max-width: 400px;
}

/* 消息项 */
.message-item {
  display: flex;
  margin-bottom: 20px;
  animation: fadeInUp 0.3s ease-out;
}

.message-item.user {
  flex-direction: row-reverse;
}

.message-item.user .message-content {
  margin-right: 12px;
  margin-left: 60px;
}

.message-item.assistant .message-content {
  margin-left: 12px;
  margin-right: 60px;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 消息头像 */
.message-avatar {
  flex-shrink: 0;
}

/* 消息内容 */
.message-content {
  flex: 1;
  min-width: 0;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.sender-name {
  font-size: 12px;
  font-weight: 600;
  color: #666;
}

.message-time {
  font-size: 11px;
  color: #999;
}

.message-body {
  background: white;
  border-radius: 12px;
  padding: 12px 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  position: relative;
}

.message-item.user .message-body {
  background: #1890ff;
  color: white;
}

.message-item.user .message-body::after {
  content: '';
  position: absolute;
  top: 10px;
  right: -8px;
  width: 0;
  height: 0;
  border-left: 8px solid #1890ff;
  border-top: 8px solid transparent;
  border-bottom: 8px solid transparent;
}

.message-item.assistant .message-body::after {
  content: '';
  position: absolute;
  top: 10px;
  left: -8px;
  width: 0;
  height: 0;
  border-right: 8px solid white;
  border-top: 8px solid transparent;
  border-bottom: 8px solid transparent;
}

/* 消息文本 */
.message-text {
  line-height: 1.6;
  word-wrap: break-word;
}

.message-text p {
  margin: 0 0 8px 0;
}

.message-text p:last-child {
  margin-bottom: 0;
}

.message-text code {
  background: #f5f5f5;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Monaco', 'Consolas', monospace;
  font-size: 12px;
}

.message-item.user .message-text code {
  background: rgba(255, 255, 255, 0.2);
}

.message-text pre {
  background: #f5f5f5;
  padding: 12px;
  border-radius: 6px;
  overflow-x: auto;
  margin: 8px 0;
}

.message-item.user .message-text pre {
  background: rgba(255, 255, 255, 0.1);
}

/* 图片消息 */
.message-image {
  text-align: center;
}

.message-image img {
  border-radius: 8px;
  cursor: pointer;
  transition: transform 0.2s;
}

.message-image img:hover {
  transform: scale(1.02);
}

.image-filename {
  margin-top: 8px;
  font-size: 12px;
  color: #666;
}

/* 文件消息 */
.message-file {
  display: flex;
  align-items: center;
  padding: 8px;
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  background: #f9f9f9;
  cursor: pointer;
  transition: background-color 0.2s;
}

.message-file:hover {
  background: #f0f0f0;
}

.file-info {
  margin-left: 12px;
  flex: 1;
}

.file-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.file-size {
  font-size: 12px;
  color: #999;
  margin-top: 2px;
}

/* 系统消息 */
.system-message {
  text-align: center;
  color: #999;
  font-size: 12px;
  font-style: italic;
  padding: 8px;
  background: #f0f0f0;
  border-radius: 6px;
}

/* 加载消息 */
.loading-message {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  opacity: 0.8;
}

.loading-content {
  margin-left: 12px;
  display: flex;
  align-items: center;
  color: #666;
  font-size: 14px;
}

/* 输入容器 */
.input-container {
  padding: 16px 20px;
  background: white;
  border-top: 1px solid #e8e8e8;
}

.input-wrapper {
  display: flex;
  align-items: flex-end;
  gap: 8px;
  background: #f9f9f9;
  border-radius: 12px;
  padding: 8px;
  border: 1px solid #d9d9d9;
  transition: border-color 0.2s;
}

.input-wrapper:focus-within {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
}

/* 上传按钮 */
.upload-buttons {
  display: flex;
  gap: 4px;
}

.upload-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 8px;
  color: #666;
  transition: all 0.2s;
}

.upload-btn:hover {
  background: #e6f7ff;
  color: #1890ff;
}

/* 消息输入框 */
.message-input {
  flex: 1;
  border: none !important;
  background: transparent !important;
  box-shadow: none !important;
  resize: none;
}

.message-input:focus {
  border: none !important;
  box-shadow: none !important;
}

.message-input .ant-input {
  border: none;
  background: transparent;
  box-shadow: none;
  padding: 6px 0;
  font-size: 14px;
  line-height: 1.5;
}

/* 发送按钮 */
.send-button {
  flex-shrink: 0;
  width: 36px;
  height: 36px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
  transition: all 0.2s;
}

.send-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
}

.send-button:active {
  transform: translateY(0);
}

.send-button:disabled {
  transform: none;
  box-shadow: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chat-window {
    border-radius: 0;
    height: 100vh;
  }
  
  .messages-container {
    padding: 16px;
  }
  
  .message-item.user .message-content {
    margin-left: 40px;
  }
  
  .message-item.assistant .message-content {
    margin-right: 40px;
  }
  
  .input-container {
    padding: 12px 16px;
  }
  
  .upload-buttons {
    flex-direction: column;
  }
}

/* 暗色主题支持 */
@media (prefers-color-scheme: dark) {
  .chat-window {
    background: #1f1f1f;
    color: #e8e8e8;
  }
  
  .messages-container {
    background: #141414;
  }
  
  .message-body {
    background: #2a2a2a;
    color: #e8e8e8;
  }
  
  .message-item.assistant .message-body::after {
    border-right-color: #2a2a2a;
  }
  
  .input-container {
    background: #1f1f1f;
    border-top-color: #333;
  }
  
  .input-wrapper {
    background: #2a2a2a;
    border-color: #404040;
  }
} 