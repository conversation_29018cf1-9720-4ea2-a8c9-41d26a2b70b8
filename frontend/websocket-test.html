<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket 连接测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover { background-color: #0056b3; }
        button:disabled { background-color: #6c757d; cursor: not-allowed; }
        #log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        input[type="text"] {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>WebSocket 连接测试工具</h1>
    
    <div class="status info">
        <strong>测试地址：</strong> 通过前端代理 /ws/{user_id} → ws://**********:8080/ws/{user_id}
    </div>
    <div class="status success">
        <strong>🎉 问题已解决：</strong>
        <br>• ✅ 后端 WebSocket 服务正常（直连测试成功）
        <br>• ✅ 前端开发服务器已重启
        <br>• ✅ Vite 代理配置正常工作
        <br>• ✅ 前端用户ID生成逻辑已修复（使用稳定ID）
        <br>• 🔄 请重新测试代理连接！
    </div>
    
    <div id="status" class="status warning">
        状态：未连接
    </div>
    
    <div>
        <button id="connectBtn" onclick="connect()">连接</button>
        <button id="disconnectBtn" onclick="disconnect()" disabled>断开连接</button>
        <button onclick="testSingleConnection()">🎯 测试代理连接</button>
        <button onclick="testDirectConnection()">🎯 测试直连后端</button>
        <button onclick="clearLog()">清空日志</button>
    </div>
    
    <div style="margin: 20px 0;">
        <h3>发送测试消息</h3>
        <input type="text" id="messageInput" placeholder="输入测试消息" disabled>
        <button id="sendBtn" onclick="sendMessage()" disabled>发送消息</button>
    </div>
    
    <h3>连接日志</h3>
    <div id="log"></div>

    <script>
        let ws = null;
        let connectBtn = document.getElementById('connectBtn');
        let disconnectBtn = document.getElementById('disconnectBtn');
        let sendBtn = document.getElementById('sendBtn');
        let messageInput = document.getElementById('messageInput');
        let statusDiv = document.getElementById('status');
        let logDiv = document.getElementById('log');

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `<span style="color: #666;">[${timestamp}]</span> <span style="color: ${getLogColor(type)};">${message}</span>`;
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function getLogColor(type) {
            switch(type) {
                case 'success': return '#28a745';
                case 'error': return '#dc3545';
                case 'warning': return '#ffc107';
                default: return '#17a2b8';
            }
        }

        function updateStatus(message, type) {
            statusDiv.className = `status ${type}`;
            statusDiv.innerHTML = `<strong>状态：</strong>${message}`;
        }

        function connect() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                log('WebSocket 已经连接', 'warning');
                return;
            }

            // 使用前端代理路径测试 WebSocket 连接
            // 注意：现在使用稳定的用户ID，避免每次都生成新的ID
            const userId = 'stable_test_user_123';
            const sessionId = 'stable_test_session_456';

            // 通过前端代理的 WebSocket 路径格式
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const host = window.location.host;

            // 添加调试信息
            log(`🔍 调试信息:`);
            log(`- 当前页面协议: ${window.location.protocol}`);
            log(`- 当前页面主机: ${window.location.host}`);
            log(`- WebSocket协议: ${protocol}`);
            log(`- 目标主机: ${host}`);

            const testUrls = [
                `${protocol}//${host}/ws/${userId}?conversation_id=${sessionId}`,
                `${protocol}//${host}/ws/${userId}`,
                `${protocol}//${host}/ws/test_user`,
                `${protocol}//${host}/ws/anonymous`,
                // 也测试直连后端（用于对比）
                `ws://**********:8080/ws/${userId}`,
                `ws://**********:8080/ws/test_user`,
            ];

            let currentUrlIndex = 0;

            function tryConnect() {
                if (currentUrlIndex >= testUrls.length) {
                    log('❌ 所有连接方式都失败了', 'error');
                    updateStatus('连接失败', 'error');
                    return;
                }

                const fullUrl = testUrls[currentUrlIndex];
                log(`尝试连接方式 ${currentUrlIndex + 1}/${testUrls.length}: ${fullUrl}`, 'info');
                updateStatus('正在连接...', 'warning');

                try {
                    ws = new WebSocket(fullUrl);

                    ws.onopen = function(event) {
                        log(`✅ WebSocket 连接成功！使用 URL: ${fullUrl}`, 'success');
                        updateStatus('已连接', 'success');
                        connectBtn.disabled = true;
                        disconnectBtn.disabled = false;
                        sendBtn.disabled = false;
                        messageInput.disabled = false;

                        // 发送一个测试心跳消息
                        setTimeout(() => {
                            if (ws && ws.readyState === WebSocket.OPEN) {
                                const heartbeat = JSON.stringify({
                                    type: 'heartbeat',
                                    timestamp: Date.now()
                                });
                                ws.send(heartbeat);
                                log('发送心跳消息: ' + heartbeat, 'info');
                            }
                        }, 1000);
                    };

                    ws.onmessage = function(event) {
                        log('📨 收到消息: ' + event.data, 'success');
                    };

                    ws.onclose = function(event) {
                        const closeReasons = {
                            1000: '正常关闭',
                            1001: '端点离开',
                            1002: '协议错误',
                            1003: '不支持的数据类型',
                            1004: '保留',
                            1005: '没有状态码',
                            1006: '异常关闭 (通常是网络问题或服务器拒绝连接)',
                            1007: '数据格式错误',
                            1008: '策略违规',
                            1009: '消息太大',
                            1010: '扩展协商失败',
                            1011: '服务器错误',
                            1015: 'TLS 握手失败'
                        };

                        const reason = closeReasons[event.code] || '未知原因';
                        log(`🔌 连接关闭 - Code: ${event.code} (${reason}), Reason: ${event.reason || '无'}`, 'warning');

                        if (event.code === 1006) {
                            log('💡 错误 1006 分析: 连接异常关闭，可能原因:', 'warning');
                            log('   - 服务器在握手过程中关闭了连接', 'warning');
                            log('   - 服务器不支持 WebSocket 协议', 'warning');
                            log('   - 需要特定的认证或头部信息', 'warning');
                            log('   - 防火墙或代理阻止了连接', 'warning');

                            // 尝试下一个 URL
                            currentUrlIndex++;
                            if (currentUrlIndex < testUrls.length) {
                                log(`⏭️ 尝试下一个连接方式...`, 'info');
                                setTimeout(tryConnect, 1000);
                                return;
                            }
                        }

                        updateStatus('连接已关闭', 'warning');
                        resetButtons();
                    };

                    ws.onerror = function(error) {
                        log('❌ WebSocket 错误详情:', 'error');
                        log(`   - 错误对象: ${error}`, 'error');
                        log(`   - 错误类型: ${error.type || '未知'}`, 'error');
                        log(`   - 当前 URL: ${fullUrl}`, 'error');
                        updateStatus('连接错误', 'error');
                    };

                    // 连接超时检测
                    setTimeout(() => {
                        if (ws && ws.readyState === WebSocket.CONNECTING) {
                            log('⏰ 连接超时，正在关闭...', 'error');
                            ws.close();
                        }
                    }, 5000);

                } catch (error) {
                    log('❌ 创建 WebSocket 失败: ' + error.message, 'error');
                    updateStatus('连接失败', 'error');
                    currentUrlIndex++;
                    if (currentUrlIndex < testUrls.length) {
                        setTimeout(tryConnect, 1000);
                    }
                }
            }

            // 开始第一次连接尝试
            tryConnect();
        }

        function disconnect() {
            if (ws) {
                ws.close();
                log('🔌 手动断开连接', 'info');
            }
        }

        function sendMessage() {
            const message = messageInput.value.trim();
            if (!message) {
                log('⚠️ 请输入消息内容', 'warning');
                return;
            }

            if (ws && ws.readyState === WebSocket.OPEN) {
                const testMessage = JSON.stringify({
                    type: 'chat',
                    content: message,
                    timestamp: Date.now()
                });
                ws.send(testMessage);
                log('📤 发送消息: ' + testMessage, 'info');
                messageInput.value = '';
            } else {
                log('❌ WebSocket 未连接，无法发送消息', 'error');
            }
        }

        function resetButtons() {
            connectBtn.disabled = false;
            disconnectBtn.disabled = true;
            sendBtn.disabled = true;
            messageInput.disabled = true;
        }

        function clearLog() {
            logDiv.innerHTML = '';
        }

        // 测试代理连接
        function testSingleConnection() {
            const userId = 'test_user_' + Date.now();
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const host = window.location.host;
            const url = `${protocol}//${host}/ws/${userId}`;

            log(`🎯 测试代理连接: ${url}`);

            const ws = new WebSocket(url);

            ws.onopen = function(event) {
                log(`✅ 代理连接成功！`);
                log(`- URL: ${url}`);
                log(`- ReadyState: ${ws.readyState}`);

                // 发送测试消息
                ws.send(JSON.stringify({
                    type: 'test',
                    message: 'Hello from proxy test',
                    user_id: userId
                }));

                // 3秒后关闭连接
                setTimeout(() => {
                    ws.close();
                    log(`🔌 代理测试连接已关闭`);
                }, 3000);
            };

            ws.onmessage = function(event) {
                log(`📨 收到消息: ${event.data}`);
            };

            ws.onerror = function(error) {
                log(`❌ 代理连接错误: ${error}`);
                log(`- URL: ${url}`);
            };

            ws.onclose = function(event) {
                log(`🔌 代理连接关闭 - Code: ${event.code}, Reason: ${event.reason || '无'}`);
            };
        }

        // 测试直连后端
        function testDirectConnection() {
            const userId = 'test_user_' + Date.now();
            const url = `ws://**********:8080/ws/${userId}`;

            log(`🎯 测试直连后端: ${url}`);

            const ws = new WebSocket(url);

            ws.onopen = function(event) {
                log(`✅ 直连后端成功！`);
                log(`- URL: ${url}`);
                log(`- ReadyState: ${ws.readyState}`);

                // 发送测试消息
                ws.send(JSON.stringify({
                    type: 'test',
                    message: 'Hello from direct connection test',
                    user_id: userId
                }));

                // 3秒后关闭连接
                setTimeout(() => {
                    ws.close();
                    log(`🔌 直连测试连接已关闭`);
                }, 3000);
            };

            ws.onmessage = function(event) {
                log(`📨 收到消息: ${event.data}`);
            };

            ws.onerror = function(error) {
                log(`❌ 直连错误: ${error}`);
                log(`- URL: ${url}`);
            };

            ws.onclose = function(event) {
                log(`🔌 直连关闭 - Code: ${event.code}, Reason: ${event.reason || '无'}`);
            };
        }

        // 回车发送消息
        messageInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });

        // 页面加载完成后的初始化
        window.onload = function() {
            log('🚀 WebSocket 测试工具已加载', 'info');
            log('点击"连接"按钮开始测试', 'info');
        };

        // 页面关闭时清理连接
        window.onbeforeunload = function() {
            if (ws) {
                ws.close();
            }
        };
    </script>
</body>
</html>
