# 环境变量示例文件
# 复制此文件为 .env.development 或 .env.production 并填入实际值

# 环境类型
NODE_ENV=development

# API 配置
VITE_API_BASE_URL=http://localhost:8080/api/v1
VITE_API_TIMEOUT=30000

# WebSocket 配置
VITE_WS_URL=ws://localhost:8080/ws
VITE_WS_RECONNECT_ATTEMPTS=5
VITE_WS_RECONNECT_INTERVAL=1000

# 聊天服务配置
VITE_CHAT_WS_URL=ws://localhost:8000

# 日志配置
VITE_LOG_LEVEL=debug
VITE_LOG_ENABLE_CONSOLE=true
VITE_LOG_ENABLE_REMOTE=false

# 地图配置
VITE_MAPBOX_ACCESS_TOKEN=your_mapbox_token_here

# 应用配置
VITE_APP_TITLE=AI智能旅行规划助手
VITE_APP_VERSION=1.0.0

# Vite 开发服务器代理配置
VITE_PROXY_API_TARGET=http://localhost:8080
VITE_PROXY_WS_TARGET=ws://localhost:8080
