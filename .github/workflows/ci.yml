name: AI Travel Planner CI/CD

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

env:
  PYTHON_VERSION: '3.10'
  NODE_VERSION: '18'

jobs:
  # 后端服务测试
  backend-tests:
    runs-on: ubuntu-latest
    
    services:
      redis:
        image: redis:7-alpine
        ports:
          - 6379:6379
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
      
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: test_password
          MYSQL_DATABASE: test_db
          MYSQL_USER: test_user
          MYSQL_PASSWORD: test_pass
        ports:
          - 3306:3306
        options: >-
          --health-cmd="mysqladmin ping"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=3
      
      qdrant:
        image: qdrant/qdrant:latest
        ports:
          - 6333:6333
        options: >-
          --health-cmd="curl -f http://localhost:6333/health || exit 1"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=3

    strategy:
      matrix:
        service: [chat-service, rag-service, user-service, agent-service, planning-service, integration-service]

    steps:
    - name: Checkout代码
      uses: actions/checkout@v4

    - name: 设置Python环境
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        cache: 'pip'

    - name: 安装Python依赖
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pytest pytest-cov pytest-asyncio pytest-mock

    - name: 安装系统依赖
      run: |
        sudo apt-get update
        sudo apt-get install -y build-essential libpq-dev

    - name: 下载语言模型
      run: |
        python -c "import nltk; nltk.download('punkt'); nltk.download('stopwords')"
        python -c "import spacy; spacy.cli.download('en_core_web_sm')"

    - name: 运行Lint检查
      run: |
        pip install flake8 black isort
        flake8 services/${{ matrix.service }} --count --select=E9,F63,F7,F82 --show-source --statistics
        black --check services/${{ matrix.service }}
        isort --check-only services/${{ matrix.service }}

    - name: 运行单元测试
      env:
        REDIS_HOST: localhost
        REDIS_PORT: 6379
        MYSQL_HOST: localhost
        MYSQL_PORT: 3306
        MYSQL_DATABASE: test_db
        MYSQL_USER: test_user
        MYSQL_PASSWORD: test_pass
        QDRANT_HOST: localhost
        QDRANT_PORT: 6333
        ENVIRONMENT: testing
      run: |
        cd services/${{ matrix.service }}
        python -m pytest tests/ -v --cov=. --cov-report=xml --cov-report=html

    - name: 上传覆盖率报告
      uses: codecov/codecov-action@v3
      with:
        file: ./services/${{ matrix.service }}/coverage.xml
        flags: backend-${{ matrix.service }}
        name: ${{ matrix.service }}-coverage

  # 前端测试
  frontend-tests:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout代码
      uses: actions/checkout@v4

    - name: 设置Node.js环境
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json

    - name: 安装前端依赖
      run: |
        cd frontend
        npm ci

    - name: 运行ESLint检查
      run: |
        cd frontend
        npm run lint

    - name: 运行TypeScript检查
      run: |
        cd frontend
        npm run type-check

    - name: 运行前端单元测试
      run: |
        cd frontend
        npm run test -- --coverage --watchAll=false

    - name: 构建前端应用
      run: |
        cd frontend
        npm run build

    - name: 上传构建产物
      uses: actions/upload-artifact@v4
      with:
        name: frontend-build
        path: frontend/dist

  # 集成测试
  integration-tests:
    runs-on: ubuntu-latest
    needs: [backend-tests, frontend-tests]
    
    steps:
    - name: Checkout代码
      uses: actions/checkout@v4

    - name: 设置Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: 构建测试镜像
      run: |
        docker compose -f deployment/docker/docker-compose.test.yml build

    - name: 运行集成测试
      run: |
        docker compose -f deployment/docker/docker-compose.test.yml up -d
        sleep 30
        python scripts/run_integration_tests.py

    - name: 收集测试日志
      if: failure()
      run: |
        docker compose -f deployment/docker/docker-compose.test.yml logs

    - name: 清理测试环境
      if: always()
      run: |
        docker compose -f deployment/docker/docker-compose.test.yml down -v

  # 安全扫描
  security-scan:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout代码
      uses: actions/checkout@v4

    - name: 运行安全扫描
      uses: github/super-linter@v4
      env:
        DEFAULT_BRANCH: main
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        VALIDATE_PYTHON_BLACK: true
        VALIDATE_PYTHON_FLAKE8: true
        VALIDATE_TYPESCRIPT_ES: true
        VALIDATE_DOCKERFILE_HADOLINT: true

    - name: Python安全检查
      run: |
        pip install safety bandit
        safety check -r requirements.txt
        bandit -r services/ -f json -o bandit-report.json

    - name: 上传安全报告
      uses: actions/upload-artifact@v4
      with:
        name: security-reports
        path: bandit-report.json

  # 代码质量检查
  code-quality:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout代码
      uses: actions/checkout@v4

    - name: SonarCloud扫描
      uses: SonarSource/sonarcloud-github-action@master
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}

  # 构建和推送Docker镜像
  build-and-push:
    runs-on: ubuntu-latest
    needs: [backend-tests, frontend-tests, integration-tests]
    if: github.ref == 'refs/heads/main'
    
    strategy:
      matrix:
        service: [chat-service, rag-service, user-service, agent-service, planning-service, integration-service]
    
    steps:
    - name: Checkout代码
      uses: actions/checkout@v4

    - name: 设置Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: 登录Docker Hub
      uses: docker/login-action@v3
      with:
        username: ${{ secrets.DOCKER_USERNAME }}
        password: ${{ secrets.DOCKER_PASSWORD }}

    - name: 提取元数据
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ secrets.DOCKER_USERNAME }}/ai-travel-${{ matrix.service }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=sha,prefix={{branch}}-

    - name: 构建并推送镜像
      uses: docker/build-push-action@v5
      with:
        context: .
        file: services/${{ matrix.service }}/Dockerfile
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

  # 部署到测试环境
  deploy-staging:
    runs-on: ubuntu-latest
    needs: [build-and-push]
    if: github.ref == 'refs/heads/develop'
    environment: staging
    
    steps:
    - name: Checkout代码
      uses: actions/checkout@v4

    - name: 部署到测试环境
      run: |
        echo "部署到测试环境..."
        # 这里可以添加实际的部署脚本
        # 例如：kubectl apply、docker-compose up等

  # 部署到生产环境
  deploy-production:
    runs-on: ubuntu-latest
    needs: [build-and-push]
    if: github.ref == 'refs/heads/main'
    environment: production
    
    steps:
    - name: Checkout代码
      uses: actions/checkout@v4

    - name: 部署到生产环境
      run: |
        echo "部署到生产环境..."
        # 这里可以添加实际的部署脚本

  # 通知
  notify:
    runs-on: ubuntu-latest
    needs: [backend-tests, frontend-tests, integration-tests]
    if: always()
    
    steps:
    - name: 通知团队
      uses: 8398a7/action-slack@v3
      if: always()
      with:
        status: ${{ job.status }}
        text: 'AI Travel Planner CI/CD Pipeline'
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }} 